import React, { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [activeSection, setActiveSection] = useState('home')

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
      
      // 检测当前活动区域
      const sections = ['home', 'services', 'cases', 'about', 'contact']
      const current = sections.find(section => {
        const element = document.getElementById(section)
        if (element) {
          const rect = element.getBoundingClientRect()
          return rect.top <= 100 && rect.bottom >= 100
        }
        return false
      })
      if (current) setActiveSection(current)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div className="App">
      {/* 导航栏 */}
      <nav className={`navbar ${isScrolled ? 'scrolled' : ''}`}>
        <div className="nav-container">
          <div className="nav-logo">
            <div className="logo-icon">🚀</div>
            <h2>小程序开发专家</h2>
          </div>
          <ul className="nav-menu">
            <li><a 
              href="#home" 
              className={activeSection === 'home' ? 'active' : ''}
              onClick={() => scrollToSection('home')}
            >首页</a></li>
            <li><a 
              href="#services" 
              className={activeSection === 'services' ? 'active' : ''}
              onClick={() => scrollToSection('services')}
            >服务</a></li>
            <li><a 
              href="#cases" 
              className={activeSection === 'cases' ? 'active' : ''}
              onClick={() => scrollToSection('cases')}
            >案例</a></li>
            <li><a 
              href="#about" 
              className={activeSection === 'about' ? 'active' : ''}
              onClick={() => scrollToSection('about')}
            >关于我们</a></li>
            <li><a 
              href="#contact" 
              className={activeSection === 'contact' ? 'active' : ''}
              onClick={() => scrollToSection('contact')}
            >联系我们</a></li>
          </ul>
          <div className="nav-cta">
            <button className="nav-button">免费咨询</button>
          </div>
        </div>
      </nav>

      {/* 首页横幅 */}
      <section id="home" className="hero">
        <div className="hero-bg">
          <div className="hero-bg-circle"></div>
          <div className="hero-bg-circle"></div>
          <div className="hero-bg-circle"></div>
        </div>
        <div className="hero-content">
          <div className="hero-badge">
            <span>🏆 行业领先</span>
            <span>💼 5年+经验</span>
          </div>
          <h1>专业小程序开发服务</h1>
          <p>为您的业务打造专属小程序解决方案，助力数字化转型，提升用户体验，实现业务增长</p>
          <div className="hero-buttons">
            <a href="#contact" className="cta-button primary">免费咨询</a>
            <a href="#cases" className="cta-button secondary">查看案例</a>
          </div>
          <div className="hero-stats">
            <div className="stat-item">
              <span className="stat-number">100+</span>
              <span className="stat-label">成功项目</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">50+</span>
              <span className="stat-label">合作客户</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">98%</span>
              <span className="stat-label">客户满意度</span>
            </div>
          </div>
        </div>
        <div className="hero-image">
          <div className="phone-mockup">
            <div className="phone-frame">
              <div className="phone-notch"></div>
              <div className="screen">
                <div className="app-grid">
                  <div className="app-item shopping">
                    <div className="app-icon">🛒</div>
                    <span>购物</span>
                  </div>
                  <div className="app-item housekeeping">
                    <div className="app-icon">🏠</div>
                    <span>家政</span>
                  </div>
                  <div className="app-item takeout">
                    <div className="app-icon">🍔</div>
                    <span>外卖</span>
                  </div>
                  <div className="app-item more">
                    <div className="app-icon">➕</div>
                    <span>更多</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section id="services" className="services">
        <div className="container">
          <div className="section-header">
            <div className="section-badge">我们的服务</div>
            <h2 className="section-title">专业的小程序开发解决方案</h2>
            <p className="section-subtitle">针对不同行业需求，提供定制化的小程序开发服务</p>
          </div>
          <div className="services-grid">
            <div className="service-card">
              <div className="service-header">
                <div className="service-icon">🛒</div>
                <div className="service-badge">热门</div>
              </div>
              <h3>购物类小程序</h3>
              <p>电商购物、商城管理、会员系统、支付集成等完整解决方案</p>
              <ul className="service-features">
                <li><span className="check">✓</span>商品展示与管理</li>
                <li><span className="check">✓</span>购物车与订单系统</li>
                <li><span className="check">✓</span>会员积分与优惠券</li>
                <li><span className="check">✓</span>支付与物流对接</li>
                <li><span className="check">✓</span>数据分析与报表</li>
              </ul>
              <div className="service-footer">
                <span className="price">¥15,000起</span>
                <button className="service-btn">了解详情</button>
              </div>
            </div>
            <div className="service-card featured">
              <div className="service-header">
                <div className="service-icon">🏠</div>
                <div className="service-badge">推荐</div>
              </div>
              <h3>家政类小程序</h3>
              <p>家政服务预约、人员管理、服务评价等专业解决方案</p>
              <ul className="service-features">
                <li><span className="check">✓</span>服务预约与派单</li>
                <li><span className="check">✓</span>服务人员管理</li>
                <li><span className="check">✓</span>客户评价系统</li>
                <li><span className="check">✓</span>服务标准制定</li>
                <li><span className="check">✓</span>实时位置追踪</li>
              </ul>
              <div className="service-footer">
                <span className="price">¥18,000起</span>
                <button className="service-btn">了解详情</button>
              </div>
            </div>
            <div className="service-card">
              <div className="service-header">
                <div className="service-icon">🍔</div>
                <div className="service-badge">新品</div>
              </div>
              <h3>外卖类小程序</h3>
              <p>外卖点餐、配送管理、商家后台等餐饮行业解决方案</p>
              <ul className="service-features">
                <li><span className="check">✓</span>在线点餐系统</li>
                <li><span className="check">✓</span>配送路线优化</li>
                <li><span className="check">✓</span>商家管理后台</li>
                <li><span className="check">✓</span>数据分析报表</li>
                <li><span className="check">✓</span>多门店管理</li>
              </ul>
              <div className="service-footer">
                <span className="price">¥20,000起</span>
                <button className="service-btn">了解详情</button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 案例展示 */}
      <section id="cases" className="cases">
        <div className="container">
          <div className="section-header">
            <div className="section-badge">成功案例</div>
            <h2 className="section-title">我们的优秀作品</h2>
            <p className="section-subtitle">每一个项目都是我们专业能力的体现</p>
          </div>
          <div className="cases-grid">
            <div className="case-card">
              <div className="case-image">
                <div className="case-icon">🛍️</div>
                <div className="case-overlay">
                  <button className="case-btn">查看详情</button>
                </div>
              </div>
              <div className="case-content">
                <div className="case-tags">
                  <span className="tag">购物类</span>
                  <span className="tag">电商</span>
                </div>
                <h3>美妆商城小程序</h3>
                <p>为知名美妆品牌开发的购物小程序，月活用户10万+，转化率提升35%</p>
                <div className="case-stats">
                  <div className="case-stat">
                    <span className="stat-value">10万+</span>
                    <span className="stat-label">月活用户</span>
                  </div>
                  <div className="case-stat">
                    <span className="stat-value">35%</span>
                    <span className="stat-label">转化率提升</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="case-card">
              <div className="case-image">
                <div className="case-icon">🧹</div>
                <div className="case-overlay">
                  <button className="case-btn">查看详情</button>
                </div>
              </div>
              <div className="case-content">
                <div className="case-tags">
                  <span className="tag">家政类</span>
                  <span className="tag">服务</span>
                </div>
                <h3>清洁服务小程序</h3>
                <p>家政服务预约平台，服务覆盖全市，用户满意度98%，订单量增长200%</p>
                <div className="case-stats">
                  <div className="case-stat">
                    <span className="stat-value">98%</span>
                    <span className="stat-label">用户满意度</span>
                  </div>
                  <div className="case-stat">
                    <span className="stat-value">200%</span>
                    <span className="stat-label">订单量增长</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="case-card">
              <div className="case-image">
                <div className="case-icon">🍕</div>
                <div className="case-overlay">
                  <button className="case-btn">查看详情</button>
                </div>
              </div>
              <div className="case-content">
                <div className="case-tags">
                  <span className="tag">外卖类</span>
                  <span className="tag">餐饮</span>
                </div>
                <h3>披萨外卖小程序</h3>
                <p>连锁披萨店外卖系统，订单量提升200%，配送效率提升40%</p>
                <div className="case-stats">
                  <div className="case-stat">
                    <span className="stat-value">200%</span>
                    <span className="stat-label">订单量提升</span>
                  </div>
                  <div className="case-stat">
                    <span className="stat-value">40%</span>
                    <span className="stat-label">配送效率提升</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 关于我们 */}
      <section id="about" className="about">
        <div className="container">
          <div className="about-content">
            <div className="about-text">
              <div className="section-badge">关于我们</div>
              <h2>专业的小程序开发团队</h2>
              <p>我们是一家专注于小程序开发的技术公司，拥有5年+的开发经验和专业的开发团队。我们致力于为客户提供高质量、高性能的小程序解决方案。</p>
              <p>我们的团队由资深前端工程师、UI/UX设计师、产品经理组成，每个项目都经过精心策划和严格测试，确保交付质量。</p>
              <div className="about-features">
                <div className="feature">
                  <div className="feature-icon">🎯</div>
                  <div className="feature-text">
                    <h4>专业定制</h4>
                    <p>根据客户需求定制开发</p>
                  </div>
                </div>
                <div className="feature">
                  <div className="feature-icon">⚡</div>
                  <div className="feature-text">
                    <h4>快速交付</h4>
                    <p>标准项目15-30天交付</p>
                  </div>
                </div>
                <div className="feature">
                  <div className="feature-icon">🛡️</div>
                  <div className="feature-text">
                    <h4>质量保证</h4>
                    <p>严格测试，确保稳定性</p>
                  </div>
                </div>
              </div>
              <div className="stats">
                <div className="stat">
                  <h3>100+</h3>
                  <p>成功项目</p>
                </div>
                <div className="stat">
                  <h3>50+</h3>
                  <p>合作客户</p>
                </div>
                <div className="stat">
                  <h3>5年+</h3>
                  <p>行业经验</p>
                </div>
                <div className="stat">
                  <h3>98%</h3>
                  <p>客户满意度</p>
                </div>
              </div>
            </div>
            <div className="about-image">
              <div className="team-illustration">
                <div className="team-avatar">👨‍💻</div>
                <div className="team-avatar">👩‍🎨</div>
                <div className="team-avatar">👨‍💼</div>
                <div className="team-avatar">👩‍🔬</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 联系我们 */}
      <section id="contact" className="contact">
        <div className="container">
          <div className="section-header">
            <div className="section-badge">联系我们</div>
            <h2 className="section-title">获取免费咨询</h2>
            <p className="section-subtitle">填写以下信息，我们的专业顾问将为您提供详细的小程序开发方案</p>
          </div>
          <div className="contact-content">
            <div className="contact-info">
              <div className="contact-card">
                <h3>📧 邮箱咨询</h3>
                <p><EMAIL></p>
                <span className="response-time">24小时内回复</span>
              </div>
              <div className="contact-card">
                <h3>📱 电话咨询</h3>
                <p>16622114136</p>
                <span className="response-time">工作日 9:00-18:00</span>
              </div>
              <div className="contact-card">
                <h3>📍 公司地址</h3>
                <p>天津市西青区新华国际大学科技园7号楼8层</p>
                <span className="response-time">欢迎实地考察</span>
              </div>
              <div className="contact-card">
                <h3>💬 在线客服</h3>
                <p>微信：gat6in</p>
                <span className="response-time">实时在线</span>
              </div>
            </div>
            <div className="contact-form">
              <form>
                <div className="form-row">
                  <input type="text" placeholder="您的姓名" required />
                  <input type="tel" placeholder="联系电话" required />
                </div>
                <input type="email" placeholder="邮箱地址" required />
                <select required>
                  <option value="">选择小程序类型</option>
                  <option value="shopping">购物类小程序</option>
                  <option value="housekeeping">家政类小程序</option>
                  <option value="takeout">外卖类小程序</option>
                  <option value="other">其他类型</option>
                </select>
                <textarea placeholder="请详细描述您的项目需求..." required></textarea>
                <button type="submit" className="submit-button">
                  <span>提交咨询</span>
                  <span className="arrow">→</span>
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* 页脚 */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <div className="footer-logo">
                <div className="logo-icon">🚀</div>
                <h3>小程序开发专家</h3>
              </div>
              <p>专业的小程序开发服务商，助力企业数字化转型</p>
              <div className="social-links">
                <a href="#" className="social-link">微信</a>
                <a href="#" className="social-link">微博</a>
                <a href="#" className="social-link">知乎</a>
              </div>
            </div>
            <div className="footer-section">
              <h4>服务项目</h4>
              <ul>
                <li>购物类小程序</li>
                <li>家政类小程序</li>
                <li>外卖类小程序</li>
                <li>定制开发</li>
                <li>技术咨询</li>
              </ul>
            </div>
            <div className="footer-section">
              <h4>解决方案</h4>
              <ul>
                <li>电商解决方案</li>
                <li>服务预约系统</li>
                <li>餐饮外卖系统</li>
                <li>企业管理系统</li>
                <li>数据分析平台</li>
              </ul>
            </div>
            <div className="footer-section">
              <h4>联系方式</h4>
              <p>电话：16622114136</p>
              <p>邮箱：<EMAIL></p>
              <p>地址：天津市西青区新华国际大学科技园7号楼8层</p>
              <button className="footer-cta">立即咨询</button>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2025 天津光虹谷科技有限公司. 保留所有权利.</p>
            <div className="footer-links">
              <a href="#">隐私政策</a>
              <a href="#">服务条款</a>
              <a href="#">网站地图</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
