/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
}

.container {
  width: 100%;
  padding: 0 40px;
  margin: 0 auto;
}

/* 导航栏 */
.navbar {
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar.scrolled {
  background: rgba(0, 0, 0, 0.95);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.3);
}

.nav-container {
  width: 100%;
  padding: 0 40px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 2rem;
  animation: float 3s ease-in-out infinite;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-logo h2 {
  color: #ffffff;
  font-weight: 700;
  font-size: 1.5rem;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2.5rem;
}

.nav-menu a {
  text-decoration: none;
  color: #ffffff;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  padding: 8px 0;
}

.nav-menu a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
  width: 100%;
}

.nav-menu a:hover,
.nav-menu a.active {
  color: #00d4ff;
}

.nav-cta {
  display: flex;
  align-items: center;
}

.nav-button {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: white;
  border: none;
  padding: 12px 24px;
  font-size: 0.95rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

/* 首页横幅 */
.hero {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 100vh;
  padding: 120px 40px 80px;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  overflow: hidden;
  width: 100%;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 150, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 0, 0.05) 0%, transparent 50%);
  z-index: 1;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(0, 212, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.hero-bg-circle:nth-child(1) {
  width: 400px;
  height: 400px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.hero-bg-circle:nth-child(2) {
  width: 300px;
  height: 300px;
  top: 60%;
  left: 5%;
  animation-delay: 2s;
}

.hero-bg-circle:nth-child(3) {
  width: 200px;
  height: 200px;
  top: 20%;
  left: 60%;
  animation-delay: 4s;
}

.hero-content {
  flex: 1;
  max-width: 600px;
  z-index: 2;
  position: relative;
}

.hero-badge {
  display: flex;
  gap: 12px;
  margin-bottom: 2rem;
}

.hero-badge span {
  background: rgba(0, 212, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 8px 16px;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: #00d4ff;
}

.hero-content h1 {
  font-size: 4rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #ffffff, #00d4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-content p {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.7;
  color: #e0e0e0;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

.cta-button {
  border: none;
  padding: 18px 36px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cta-button.primary {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: white;
  box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

.cta-button.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.6);
}

.cta-button.secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(0, 212, 255, 0.5);
  backdrop-filter: blur(10px);
}

.cta-button.secondary:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.8);
  transform: translateY(-2px);
}

.hero-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 900;
  color: #00d4ff;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  color: #e0e0e0;
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  position: relative;
}

.phone-mockup {
  position: relative;
  animation: float 4s ease-in-out infinite;
}

.phone-frame {
  width: 350px;
  height: 700px;
  background: linear-gradient(145deg, #1a1a2e, #16213e);
  border-radius: 50px;
  padding: 12px;
  box-shadow: 
    0 30px 60px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.phone-notch {
  position: absolute;
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
  width: 140px;
  height: 30px;
  background: #1a1a2e;
  border-radius: 0 0 25px 25px;
  z-index: 2;
}

.screen {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #0f0f23, #1a1a2e);
  border-radius: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
  padding: 50px;
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 25px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.app-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 212, 255, 0.5);
}

.app-item.shopping { 
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 107, 107, 0.1));
  border-color: rgba(255, 107, 107, 0.3);
}
.app-item.housekeeping { 
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 212, 255, 0.1));
  border-color: rgba(0, 212, 255, 0.3);
}
.app-item.takeout { 
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
  border-color: rgba(255, 193, 7, 0.3);
}
.app-item.more { 
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.2), rgba(156, 39, 176, 0.1));
  border-color: rgba(156, 39, 176, 0.3);
}

.app-icon {
  font-size: 3rem;
}

.app-item span {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

/* 服务介绍 */
.services {
  padding: 120px 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  position: relative;
  color: white;
  width: 100%;
}

.services::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 10% 20%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(255, 0, 150, 0.05) 0%, transparent 50%);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
  z-index: 2;
}

.section-badge {
  display: inline-block;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: white;
  padding: 10px 24px;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.section-title {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  color: #ffffff;
  background: linear-gradient(135deg, #ffffff, #00d4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.3rem;
  color: #b0b0b0;
  max-width: 700px;
  margin: 0 auto;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  position: relative;
  z-index: 2;
}

.service-card {
  background: rgba(255, 255, 255, 0.05);
  padding: 3rem;
  border-radius: 30px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-20px);
  box-shadow: 0 30px 70px rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 212, 255, 0.3);
}

.service-card.featured {
  border: 2px solid #00d4ff;
  transform: scale(1.05);
  background: rgba(0, 212, 255, 0.05);
}

.service-card.featured:hover {
  transform: scale(1.05) translateY(-20px);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.service-icon {
  font-size: 3.5rem;
}

.service-badge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.service-card h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #ffffff;
}

.service-card p {
  color: #b0b0b0;
  margin-bottom: 2rem;
  line-height: 1.7;
  font-size: 1.1rem;
}

.service-features {
  list-style: none;
  margin-bottom: 2rem;
}

.service-features li {
  padding: 1rem 0;
  color: #d0d0d0;
  position: relative;
  padding-left: 2.5rem;
  display: flex;
  align-items: center;
  font-size: 1rem;
}

.check {
  color: #00d4ff;
  font-weight: bold;
  font-size: 1.3rem;
  margin-right: 0.75rem;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #00d4ff;
}

.service-btn {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.service-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 212, 255, 0.3);
}

/* 案例展示 */
.cases {
  padding: 120px 0;
  background: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
  color: white;
  width: 100%;
}

.cases::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 0, 150, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.05) 0%, transparent 50%);
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
  position: relative;
  z-index: 2;
}

.case-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 25px;
  overflow: hidden;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
  transition: all 0.4s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.case-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 30px 70px rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 212, 255, 0.3);
}

.case-image {
  position: relative;
  height: 250px;
  background: linear-gradient(135deg, #0f0f23, #1a1a2e);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.case-icon {
  font-size: 5rem;
  z-index: 1;
}

.case-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 212, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.case-card:hover .case-overlay {
  opacity: 1;
}

.case-btn {
  background: white;
  color: #0f0f23;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.case-btn:hover {
  transform: scale(1.05);
}

.case-content {
  padding: 2.5rem;
}

.case-tags {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.tag {
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.case-content h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #ffffff;
}

.case-content p {
  color: #b0b0b0;
  margin-bottom: 2rem;
  line-height: 1.6;
  font-size: 1.1rem;
}

.case-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.case-stat {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-value {
  display: block;
  font-size: 1.75rem;
  font-weight: 800;
  color: #00d4ff;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #b0b0b0;
  font-weight: 500;
}

/* 关于我们 */
.about {
  padding: 120px 0;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
  color: white;
  width: 100%;
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 30% 70%, rgba(255, 193, 7, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(0, 212, 255, 0.05) 0%, transparent 50%);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 2;
}

.about-text h2 {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  color: #ffffff;
  background: linear-gradient(135deg, #ffffff, #00d4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-text p {
  font-size: 1.2rem;
  color: #b0b0b0;
  margin-bottom: 1rem;
  line-height: 1.7;
}

.about-features {
  display: grid;
  gap: 2rem;
  margin: 2.5rem 0;
}

.feature {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.feature-icon {
  font-size: 2.5rem;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-text h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.feature-text p {
  font-size: 1rem;
  color: #b0b0b0;
  margin: 0;
}

.stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.stat {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.stat h3 {
  font-size: 3rem;
  font-weight: 900;
  color: #00d4ff;
  margin-bottom: 0.5rem;
}

.stat p {
  color: #b0b0b0;
  font-weight: 500;
  margin: 0;
  font-size: 1.1rem;
}

.team-illustration {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
  justify-items: center;
}

.team-avatar {
  font-size: 4rem;
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.team-avatar:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 212, 255, 0.5);
}

/* 联系我们 */
.contact {
  padding: 120px 0;
  background: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
  color: white;
  width: 100%;
}

.contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 10% 90%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 10%, rgba(255, 0, 150, 0.05) 0%, transparent 50%);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  position: relative;
  z-index: 2;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.contact-card {
  background: rgba(255, 255, 255, 0.05);
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.contact-card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 212, 255, 0.3);
}

.contact-card h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #ffffff;
}

.contact-card p {
  color: #d0d0d0;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.response-time {
  font-size: 0.9rem;
  color: #00d4ff;
}

.contact-form form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.contact-form input,
.contact-form select,
.contact-form textarea {
  padding: 1.2rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  backdrop-filter: blur(10px);
}

.contact-form input::placeholder,
.contact-form textarea::placeholder {
  color: #b0b0b0;
}

.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: #00d4ff;
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
  background: rgba(255, 255, 255, 0.08);
}

.contact-form textarea {
  height: 140px;
  resize: vertical;
}

.submit-button {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: white;
  border: none;
  padding: 1.2rem;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.submit-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 212, 255, 0.4);
}

.arrow {
  transition: transform 0.3s ease;
}

.submit-button:hover .arrow {
  transform: translateX(8px);
}

/* 页脚 */
.footer {
  background: linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
  color: white;
  padding: 100px 0 40px;
  width: 100%;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 150, 0.03) 0%, transparent 50%);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 1.5rem;
}

.footer-logo .logo-icon {
  font-size: 2.5rem;
}

.footer-logo h3 {
  color: #00d4ff;
  font-size: 1.75rem;
}

.footer-section p {
  color: #b0b0b0;
  margin-bottom: 2rem;
  line-height: 1.6;
  font-size: 1.1rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  color: #808080;
  text-decoration: none;
  padding: 10px 20px;
  border: 1px solid #333;
  border-radius: 25px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.social-link:hover {
  color: #00d4ff;
  border-color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
}

.footer-section h4 {
  margin-bottom: 2rem;
  color: #00d4ff;
  font-size: 1.3rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section li {
  padding: 0.75rem 0;
  color: #b0b0b0;
  transition: color 0.3s ease;
  font-size: 1rem;
}

.footer-section li:hover {
  color: #00d4ff;
}

.footer-cta {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: #000000;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1.5rem;
}

.footer-cta:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 212, 255, 0.3);
}

.footer-bottom {
  text-align: center;
  padding-top: 3rem;
  border-top: 1px solid #333;
  color: #808080;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.footer-links a {
  color: #808080;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #00d4ff;
}

/* 动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 0 30px;
  }
  
  .nav-container {
    padding: 0 30px;
  }
  
  .hero {
    padding: 120px 30px 80px;
  }
}

@media (max-width: 1024px) {
  .hero-content h1 {
    font-size: 3.5rem;
  }
  
  .section-title {
    font-size: 3rem;
  }
  
  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
  
  .phone-frame {
    width: 320px;
    height: 640px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }
  
  .nav-container {
    padding: 0 20px;
  }
  
  .nav-menu {
    display: none;
  }
  
  .hero {
    flex-direction: column;
    text-align: center;
    padding: 100px 20px 60px;
  }
  
  .hero-content h1 {
    font-size: 3rem;
  }
  
  .hero-content p {
    font-size: 1.2rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-stats {
    justify-content: center;
  }
  
  .phone-frame {
    width: 300px;
    height: 600px;
    margin-top: 2rem;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .about-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .contact-content {
    grid-template-columns: 1fr;
  }
  
  .contact-info {
    grid-template-columns: 1fr;
  }
  
  .stats {
    grid-template-columns: 1fr;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }
  
  .nav-container {
    padding: 0 15px;
  }
  
  .hero {
    padding: 100px 15px 60px;
  }
  
  .hero-content h1 {
    font-size: 2.5rem;
  }
  
  .service-card,
  .case-card {
    padding: 2rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .phone-frame {
    width: 280px;
    height: 560px;
  }
}


